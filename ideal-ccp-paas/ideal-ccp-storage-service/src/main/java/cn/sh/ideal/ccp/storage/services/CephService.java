package cn.sh.ideal.ccp.storage.services;

import cn.idealio.lib.exception.ResourceAlreadyExistsException;
import cn.idealio.lib.lang.CollectionUtils;
import cn.sh.ideal.ccp.api.fileStorage.CephRestApi;
import cn.sh.ideal.ccp.api.fileStorage.args.CreateCephRestClusterUserArgs;
import cn.sh.ideal.ccp.api.fileStorage.enums.CephNfsAccessType;
import cn.sh.ideal.ccp.api.fileStorage.enums.CephNfsSquash;
import cn.sh.ideal.ccp.api.fileStorage.enums.CephStorageBackendType;
import cn.sh.ideal.ccp.api.fileStorage.pojo.*;
import cn.sh.ideal.ccp.storage.configure.CephRestProperties;
import cn.sh.ideal.ccp.storage.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: bianJie
 * @date: 2025/4/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CephService {

  private static final String CLIENT = "client.";
  private final CephRestApi cephRestApi;
  private final CephClusterUserRepository cephClusterUserRepository;
  private final CephNfsGaneshaClusterRepository cephNfsGaneshaClusterRepository;
  private final CephRestProperties cephRestProperties;
  private final StorageInstanceRepository instanceRepository;

  /**
   * 组装创建ceph cluster user所需参数
   *
   * @param entityName 名称
   * @param accessType 权限
   * @param path       路径
   */
  @NotNull
  private static CreateCephRestClusterUserArgs getCreateCephRestClusterUserArgs(@NotNull String entityName,
                                                                                @NotNull CephNfsAccessType accessType,
                                                                                @NotNull String path) {
    CreateCephRestClusterUserArgs args = new CreateCephRestClusterUserArgs();
    args.setUserEntity(entityName);
    List<CreateCephRestClusterUserArgs.Capabilities> capabilities = new ArrayList<>();
    CreateCephRestClusterUserArgs.Capabilities mon = new CreateCephRestClusterUserArgs.Capabilities();
    mon.setEntity("mon");
    mon.setCap("allow r");
    capabilities.add(mon);
    switch (accessType) {
      case RW -> {
        CreateCephRestClusterUserArgs.Capabilities osd = new CreateCephRestClusterUserArgs.Capabilities();
        osd.setEntity("osd");
        osd.setCap("allow rw");
        capabilities.add(osd);

        CreateCephRestClusterUserArgs.Capabilities mds = new CreateCephRestClusterUserArgs.Capabilities();
        mds.setEntity("mds");
        mds.setCap(String.format("allow rw path=%s", path));
        capabilities.add(mds);
      }
      case RO -> {
        CreateCephRestClusterUserArgs.Capabilities osd = new CreateCephRestClusterUserArgs.Capabilities();
        osd.setEntity("osd");
        osd.setCap("allow r");
        capabilities.add(osd);

        CreateCephRestClusterUserArgs.Capabilities mds = new CreateCephRestClusterUserArgs.Capabilities();
        mds.setEntity("mds");
        mds.setCap(String.format("allow r path=%s", path));
        capabilities.add(mds);
      }
    }
    args.setCapabilities(capabilities);
    return args;
  }

  public CephClusterUserInfo addClusterUser(@NotNull String storageUuid,
                                            @NotNull String accountId,
                                            @NotNull String entityName,
                                            @NotNull CephNfsAccessType accessType) {
    if (cephClusterUserRepository.existsByClusterUserEntity(entityName)) {
      throw new ResourceAlreadyExistsException("名称已存在").setPrintStackTrace(false);
    }
    StorageInstanceInfo instanceInfo = instanceRepository.findByUuid(storageUuid)
      .map(StorageInstanceDO::toEntity).orElseThrow(() -> new IllegalArgumentException("用户存储实例不存在"));
    String path = instanceInfo.getMountPath();
    CreateCephRestClusterUserArgs args = getCreateCephRestClusterUserArgs(entityName, accessType, path);
    cephRestApi.addClusterUser(args);
    String key = getClusterUserKey(args.getUserEntity());
    CephClusterUserInfo info = cephClusterUserRepository.saveAndFlush(CephClusterUserDO.of(storageUuid, accountId, key,
      args.getUserEntity(), accessType, path, "FALSE")).toEntity();
    String name = info.getClusterUserEntity().startsWith(CLIENT) ?
      info.getClusterUserEntity().substring(7) : info.getClusterUserEntity();
    info.setMountCommand(String.format("mount -t ceph %s:%s /mnt/cephfs -o name=%s,secret=%s",
      cephRestProperties.getClusterHost(), info.getClusterUserMount(), name, info.getClusterUserKey()));
    return info;
  }

  public CephClusterUserInfo addClusterUserStorage(@NotNull String storageUuid,
                                                   @NotNull String bottomPath,
                                                   @NotNull String accountId,
                                                   @NotNull String entityName,
                                                   @NotNull CephNfsAccessType accessType) {
    CreateCephRestClusterUserArgs args = getCreateCephRestClusterUserArgs(entityName, accessType, bottomPath);
    cephRestApi.addClusterUser(args);
    String key = getClusterUserKey(args.getUserEntity());
    return cephClusterUserRepository.saveAndFlush(CephClusterUserDO.of(storageUuid, accountId, key,
      args.getUserEntity(), accessType, bottomPath, "TRUE")).toEntity();
  }

  public CephClusterUserInfo updateClusterUser(@NotNull Long id,
                                               @NotNull CephNfsAccessType accessType) {
    CephClusterUserDO cephClusterUserDO = cephClusterUserRepository.findById(id)
      .orElseThrow(() -> new IllegalArgumentException("ceph集群用户信息不存在"));
    CreateCephRestClusterUserArgs args = getCreateCephRestClusterUserArgs(cephClusterUserDO.getClusterUserEntity(),
      accessType, cephClusterUserDO.getClusterUserMount());
    cephRestApi.updateClusterUser(args);
    cephClusterUserDO.setClusterUserPermission(accessType);
    CephClusterUserInfo info = cephClusterUserRepository.saveAndFlush(cephClusterUserDO).toEntity();
    String name = info.getClusterUserEntity().startsWith(CLIENT) ?
      info.getClusterUserEntity().substring(7) : info.getClusterUserEntity();
    info.setMountCommand(String.format("mount -t ceph %s:%s /mnt/cephfs -o name=%s,secret=%s",
      cephRestProperties.getClusterHost(), info.getClusterUserMount(), name, info.getClusterUserKey()));
    return info;
  }

  public void deleteClusterUser(@NotNull Long id) {
    CephClusterUserDO cephClusterUserDO = cephClusterUserRepository.findById(id)
      .orElseThrow(() -> new IllegalArgumentException("ceph集群用户信息不存在"));
    cephRestApi.deleteClusterUser(cephClusterUserDO.getClusterUserEntity());
    cephClusterUserRepository.delete(cephClusterUserDO);
  }

  public List<CephClusterUserInfo> getAllClusterUsers(@NotNull String storageUuid,
                                                      @NotNull String accountId) {
    List<CephClusterUserInfo> infos =
      cephClusterUserRepository.findAllByUuidAndAccountIdAndStorageUserFlag(storageUuid, accountId, "FALSE")
        .stream().map(CephClusterUserDO::toEntity).toList();
    infos.forEach(info -> {
      String name = info.getClusterUserEntity().startsWith(CLIENT) ?
        info.getClusterUserEntity().substring(7) : info.getClusterUserEntity();
      info.setMountCommand(String.format("mount -t ceph %s:%s /mnt/cephfs -o name=%s,secret=%s",
        cephRestProperties.getClusterHost(), info.getClusterUserMount(), name, info.getClusterUserKey()));
    });
    return infos;
  }

  public String getClusterUserKey(@NotNull String userEntity) {
    return cephRestApi.getClusterUserKey(userEntity);
  }

  //-------------------------------------------------以上是ceph集群用户相关--------------------------------------------------


  public void addNfsGaneshaExport(@NotNull String storageUuid,
                                  @NotNull String accountId,
                                  @NotNull CephNfsAccessType accessType,
                                  @NotNull CephNfsSquash squash) {
    String cluster = cephRestApi.getNfsGaneshaCluster();
    String volumeName = cephRestApi.getNfsGaneshaVolume().getFirst().getName();
    StorageInstanceInfo instanceInfo = instanceRepository.findByUuid(storageUuid)
      .map(StorageInstanceDO::toEntity).orElseThrow(() -> new IllegalArgumentException("用户存储实例不存在"));
    String path = instanceInfo.getMountPath();
    CephRestCephFsPathInfo info = cephRestApi.getNfsGaneshaCephFSPath(path);
    String pseudo = "/" + UUID.randomUUID();
    if (info != null && !CollectionUtils.isEmpty(info.getPaths())) {
      CephStorageBackendType storageBackendType = CephStorageBackendType.CEPH;
      CephRestNfsGaneshaExportInfo exportInfo = cephRestApi.addNfsGaneshaExport(accessType, squash, path,
        pseudo, cluster, volumeName, storageBackendType);

      String client = CLIENT + exportInfo.getFsal().getUserId();
      CephRestClusterUsersInfo usersInfo = new CephRestClusterUsersInfo();
      List<CephRestClusterUsersInfo> usersInfos = cephRestApi.queryAllClusterUser();
      for (CephRestClusterUsersInfo user : usersInfos) {
        if (user.getEntity().equals(client)) {
          usersInfo = user;
        }
      }
      CreateCephRestClusterUserArgs args = new CreateCephRestClusterUserArgs();
      args.setUserEntity(usersInfo.getEntity());
      nfsAddClusterUser(storageUuid, accountId, usersInfo, args, usersInfo.getKey(), accessType, path);
      cephNfsGaneshaClusterRepository.saveAndFlush(CephNfsGaneshaClusterDO.of(cluster, volumeName, path,
        storageUuid, accountId, pseudo, storageBackendType, accessType, squash, client, exportInfo.getExportId()));
    }
  }

  public void updateNfsGaneshaExport(@NotNull Long id,
                                     @NotNull CephNfsAccessType accessType,
                                     @NotNull CephNfsSquash squash) {
    CephNfsGaneshaClusterDO clusterDO = cephNfsGaneshaClusterRepository.findById(id)
      .orElseThrow(() -> new IllegalArgumentException("ceph的nfs信息不存在"));
    Integer exportId = clusterDO.getCephNfsExportId();
    String clusterId = clusterDO.getCephNfsCluster();
    CephRestNfsGaneshaExportInfo exportInfo = cephRestApi.getNfsGaneshaExportInfo(exportId, clusterId);
    cephRestApi.updateNfsGaneshaExport(accessType, squash, exportId, clusterId, exportInfo);
    clusterDO.setCephNfsAccessType(accessType);
    clusterDO.setCephNfsSquash(squash);
    cephNfsGaneshaClusterRepository.saveAndFlush(clusterDO);
  }

  public void deleteNfsGaneshaExport(@NotNull Long id) {
    CephNfsGaneshaClusterDO clusterDO = cephNfsGaneshaClusterRepository.findById(id)
      .orElseThrow(() -> new IllegalArgumentException("ceph的nfs信息不存在"));
    Integer exportId = clusterDO.getCephNfsExportId();
    String clusterId = clusterDO.getCephNfsCluster();
    cephRestApi.deleteNfsGaneshaExport(exportId, clusterId);
    cephNfsGaneshaClusterRepository.delete(clusterDO);
    cephClusterUserRepository.findByClusterUserEntity(clusterDO.getCephNfsClient())
      .ifPresent(cephClusterUserRepository::delete);
  }

  public List<CephNfsExportInfo> findAllNfsGaneshaExport(@NotNull String storageUuid,
                                                         @NotNull String accountId) {
    List<CephNfsExportInfo> infos = cephNfsGaneshaClusterRepository.findAllByUuidAndAccountId(storageUuid, accountId)
      .stream().map(CephNfsGaneshaClusterDO::toEntity).toList();
    List<CephClusterUserInfo> userInfos =
      cephClusterUserRepository.findAllByUuidAndAccountIdAndStorageUserFlag(storageUuid, accountId, "FALSE")
        .stream().map(CephClusterUserDO::toEntity).toList();
    Map<String, String> map = userInfos.stream()
      .collect(Collectors.toMap(CephClusterUserInfo::getClusterUserEntity, CephClusterUserInfo::getClusterUserKey));
    // 挂载命令：mount -t ceph 集群host:/ 用户输入的目录 -o name=入参的userEntity,secret=这里的key
    infos.forEach(info -> {
      String key = map.get(info.getCephNfsClient());
      info.setCommand(String.format("mount -t ceph %s:/ %s -o name=%s,secret=%s",
        cephRestProperties.getClusterHost(), info.getCephNfsClusterPath(), info.getCephNfsClient(), key));
    });
    return infos;
  }

  private void nfsAddClusterUser(@NotNull String storageUuid,
                                 @NotNull String accountId,
                                 @NotNull CephRestClusterUsersInfo usersInfo,
                                 @NotNull CreateCephRestClusterUserArgs args,
                                 @NotNull String key,
                                 @NotNull CephNfsAccessType accessType,
                                 @NotNull String path) {
    List<CreateCephRestClusterUserArgs.Capabilities> capabilities = new ArrayList<>();
    CephRestClusterUsersInfo.Caps caps = usersInfo.getCaps();
    if (caps != null && StringUtils.isNotBlank(caps.getMds())) {
      CreateCephRestClusterUserArgs.Capabilities cap = new CreateCephRestClusterUserArgs.Capabilities();
      cap.setEntity("mds");
      cap.setCap(caps.getMds());
      capabilities.add(cap);
    } else if (caps != null && StringUtils.isNotBlank(caps.getMon())) {
      CreateCephRestClusterUserArgs.Capabilities cap = new CreateCephRestClusterUserArgs.Capabilities();
      cap.setEntity("mon");
      cap.setCap(caps.getMon());
      capabilities.add(cap);
    } else if (caps != null && StringUtils.isNotBlank(caps.getMgr())) {
      CreateCephRestClusterUserArgs.Capabilities cap = new CreateCephRestClusterUserArgs.Capabilities();
      cap.setEntity("mgr");
      cap.setCap(caps.getMgr());
      capabilities.add(cap);
    } else if (caps != null && StringUtils.isNotBlank(caps.getOsd())) {
      CreateCephRestClusterUserArgs.Capabilities cap = new CreateCephRestClusterUserArgs.Capabilities();
      cap.setEntity("osd");
      cap.setCap(caps.getOsd());
      capabilities.add(cap);
    }
    args.setCapabilities(capabilities);
    cephClusterUserRepository.saveAndFlush(CephClusterUserDO.of(storageUuid, accountId, key, args.getUserEntity(),
      accessType, path, "FALSE"));
  }


  public List<CephRestClientInfo.ClientData> getCephClient() {
    Integer fsId = cephRestApi.getNfsGaneshaVolume().getFirst().getId();
    return cephRestApi.getCephClient(fsId);
  }

}
