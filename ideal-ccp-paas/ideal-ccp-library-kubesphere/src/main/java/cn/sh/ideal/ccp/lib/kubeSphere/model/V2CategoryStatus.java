/*
 * KS API
 * KubeSphere OpenAPI
 *
 * The version of the OpenAPI document: v4.1.1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package cn.sh.ideal.ccp.lib.kubeSphere.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * CategoryStatus defines the observed state of Category
 */
@JsonPropertyOrder({
  V2CategoryStatus.JSON_PROPERTY_TOTAL
})
@JsonTypeName("v2.CategoryStatus")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-01-27T10:00:00.000000000+08:00[GMT+08:00]", comments = "Generator version: 7.13.0")
public class V2CategoryStatus {

  public static final String JSON_PROPERTY_TOTAL = "total";
  @javax.annotation.Nonnull
  private Integer total;

  public V2CategoryStatus() {
  }

  public V2CategoryStatus total(@javax.annotation.Nonnull Integer total) {
    this.total = total;
    return this;
  }

  /**
   * Get total
   * @return total
   */
  @javax.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TOTAL)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public Integer getTotal() {
    return total;
  }

  @JsonProperty(JSON_PROPERTY_TOTAL)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTotal(@javax.annotation.Nonnull Integer total) {
    this.total = total;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    V2CategoryStatus v2CategoryStatus = (V2CategoryStatus) o;
    return Objects.equals(this.total, v2CategoryStatus.total);
  }

  @Override
  public int hashCode() {
    return Objects.hash(total);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class V2CategoryStatus {\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}
