package cn.sh.ideal.ccp.container.model;

import cn.sh.ideal.ccp.api.spec.SpecFlavorsApi;
import cn.sh.ideal.ccp.api.storage.StorageInstanceV2Api;
import cn.sh.ideal.ccp.container.build.KubeSphereClient;
import cn.sh.ideal.ccp.container.configure.ContainerProperties;
import cn.sh.ideal.ccp.lib.kubeSphere.model.V2ApplicationList;
import cn.sh.ideal.ccp.lib.kubeSphere.model.V2Category;
import cn.sh.ideal.ccp.lib.kubeSphere.model.V2CategoryList;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class KubeSphereAppsApi {

  private final KubeSphereClient kubeSphereClient;
  private final ContainerProperties properties;


  /**
   * 查看应用商店应用列表
   */
  public V2ApplicationList queryApps() {
    return kubeSphereClient.getDefaultApi()
      .listAppsWithResponseSpec()
      .bodyToMono(V2ApplicationList.class)
      .block();
  }


  /**
   * 查询应用分类
   */
  public V2CategoryList queryCategories() {
    return kubeSphereClient.getDefaultApi()
      .listCategoriesWithResponseSpec()
      .bodyToMono(V2CategoryList.class)
      .block();
  }

  /**
   * 创建应用分类
   */
  public V2Category createCategory(@Nonnull V2Category category) {
    return kubeSphereClient.getDefaultApi()
      .createOrUpdateCategoryWithResponseSpec(category)
      .bodyToMono(V2Category.class)
      .block();
  }

  /**
   * 删除应用分类
   */
  public void deleteCategory(@Nonnull String name) {
    kubeSphereClient.getDefaultApi()
      .deleteCategory(name)
      .block();
  }

  






}
